name: <PERSON> Checkstyle

on: [push]

jobs:
  tests:
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: [ ubuntu-latest ]
        php-versions: [ '8.0' ]
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          ref: ${{ github.head_ref }}

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: json, mbstring, openssl, sqlite3
          coverage: none

      - name: Install Composer dependencies
        run: composer update --no-progress --no-suggest --prefer-dist --optimize-autoloader

      - name: <PERSON>
        run: make rector
