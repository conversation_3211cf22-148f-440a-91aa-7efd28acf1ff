{"name": "spomky-labs/aes-key-wrap", "description": "AES Key Wrap for PHP.", "type": "library", "license": "MIT", "keywords": ["A128KW", "A192KW", "A256KW", "AES", "Key", "Wrap", "Padding", "RFC3394", "RFC5649"], "homepage": "https://github.com/Spomky-Labs/aes-key-wrap", "authors": [{"name": "Florent <PERSON>", "homepage": "https://github.com/Spomky-Labs/aes-key-wrap/contributors"}], "autoload": {"psr-4": {"AESKW\\": "src/"}}, "autoload-dev": {"psr-4": {"AESKW\\Tests\\": "tests/"}}, "require": {"php": ">=8.0", "ext-mbstring": "*", "ext-openssl": "*"}, "require-dev": {"phpunit/phpunit": "^9.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-beberlei-assert": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpstan/extension-installer": "^1.1", "symplify/easy-coding-standard": "^10.0", "rector/rector": "^0.12.5", "infection/infection": "^0.25.4"}}