<?php

require '../token.php'; // Inclure le fichier token.php pour la vérification des jetons

if ($_SERVER["REQUEST_METHOD"] == "GET") {
    // Vérifier le jeton d'authentification
    $headers = apache_request_headers(); // Récupérer les en-têtes de la requête
    if (isset($headers['Authorization']) && !empty($headers['Authorization'])) {
        $token = explode(' ', $headers['Authorization'])[1]; // Récupérer le jeton de l'en-tête Authorization
        if (!verifyToken($token, $headers)) {
            // Jeton invalide, refuser l'accès
            http_response_code(401);
            echo json_encode(array("message" => "Accès non autorisé. Jeton invalide."));
            exit; // Arrêter l'exécution du script
        }
    } else {
        // Jeton manquant, refuser l'accès
        http_response_code(401);
        echo json_encode(array("message" => "Accès non autorisé. Jeton manquant."));
        exit; // Arrêter l'exécution du script
    }
} else {
    // Méthode de requête non autorisée
    http_response_code(405);
    echo json_encode(array("message" => "Méthode non autorisée."));
}

// Si le jeton est valide, continuer avec votre logique pour obtenir les données des utilisateurs et renvoyer la réponse
// Exemple :
// $usersData = ...; // Obtenir les données des utilisateurs depuis la base de données
// echo json_encode($usersData);
