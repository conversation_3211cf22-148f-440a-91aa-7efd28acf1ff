<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html lang="en">
<head>
<meta name="generator" content="AWStats 7.8 (build 20200416) from config file awstats.api.callitris-distribution.com.conf (http://www.awstats.org)">
<meta name="robots" content="noindex,nofollow">
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<meta http-equiv="description" content="Awstats - Advanced Web Statistics for api.callitris-distribution.com (2025-01) - errors404">
<title>Statistics for api.callitris-distribution.com (2025-01) - errors404</title>
<style type="text/css">
body { font: 11px verdana, arial, helvetica, sans-serif; background-color: #FFFFFF; margin-top: 0; margin-bottom: 0; }
.aws_bodyl  { }
.aws_border { border-collapse: collapse; background-color: #CCCCDD; padding: 1px 1px 1px 1px; margin-top: 0px; margin-bottom: 0px; }
.aws_title  { font: 13px verdana, arial, helvetica, sans-serif; font-weight: bold; background-color: #CCCCDD; text-align: center; margin-top: 0; margin-bottom: 0; padding: 1px 1px 1px 1px; color: #000000; }
.aws_blank  { font: 13px verdana, arial, helvetica, sans-serif; background-color: #FFFFFF; text-align: center; margin-bottom: 0; padding: 1px 1px 1px 1px; }
.aws_data {
	background-color: #FFFFFF;
	border-top-width: 1px;   
	border-left-width: 0px;  
	border-right-width: 0px; 
	border-bottom-width: 0px;
}
.aws_formfield { font: 13px verdana, arial, helvetica; }
.aws_button {
	font-family: arial,verdana,helvetica, sans-serif;
	font-size: 12px;
	border: 1px solid #ccd7e0;
	background-image : url(/awstats-icon/other/button.gif);
}
th		{ border-color: #ECECEC; border-left-width: 0px; border-right-width: 1px; border-top-width: 0px; border-bottom-width: 1px; padding: 1px 2px 1px 1px; font: 11px verdana, arial, helvetica, sans-serif; text-align:center; color: #000000; }
th.aws	{ border-color: #ECECEC; border-left-width: 0px; border-right-width: 1px; border-top-width: 0px; border-bottom-width: 1px; padding: 1px 2px 1px 1px; font-size: 13px; font-weight: bold; }
td		{ border-color: #ECECEC; border-left-width: 0px; border-right-width: 1px; border-top-width: 0px; border-bottom-width: 1px; font: 11px verdana, arial, helvetica, sans-serif; text-align:center; color: #000000; }
td.aws	{ border-color: #ECECEC; border-left-width: 0px; border-right-width: 1px; border-top-width: 0px; border-bottom-width: 1px; font: 11px verdana, arial, helvetica, sans-serif; text-align:left; color: #000000; padding: 0px;}
td.awsm	{ border-left-width: 0px; border-right-width: 0px; border-top-width: 0px; border-bottom-width: 0px; font: 11px verdana, arial, helvetica, sans-serif; text-align:left; color: #000000; padding: 0px; }
b { font-weight: bold; }
a { font: 11px verdana, arial, helvetica, sans-serif; }
a:link    { color: #0011BB; text-decoration: none; }
a:visited { color: #0011BB; text-decoration: none; }
a:hover   { color: #605040; text-decoration: underline; }
.currentday { font-weight: bold; }
</style>
</head>

<body style="margin-top: 0px">
<a name="top"></a>



<a name="menu">&nbsp;</a>
<form name="FormDateFilter" action="/cgi-bin/awstats.pl?config=api.callitris-distribution.com&amp;staticlinks&amp;output=errors404" style="padding: 0px 0px 20px 0px; margin-top: 0">
<table class="aws_border" border="0" cellpadding="2" cellspacing="0" width="100%">
<tr><td>
<table class="aws_data sortable" border="0" cellpadding="1" cellspacing="0" width="100%">
<tr><td class="aws" valign="middle"><b>Statistics for:</b>&nbsp;</td><td class="aws" valign="middle"><span style="font-size: 14px;">api.callitris-distribution.com</span></td><td align="right" rowspan="3"><a href="http://www.awstats.org" target="awstatshome"><img src="/awstats-icon/other/awstats_logo6.png" border="0" alt='Awstats Web Site' title='Awstats Web Site' /></a></td></tr>
<tr valign="middle"><td class="aws" valign="middle" width="150"><b>Last Update:</b>&nbsp;</td><td class="aws" valign="middle"><span style="font-size: 12px;">09 Jan 2025 - 00:04</span></td></tr>
<tr><td class="aws" valign="middle"><b>Reported period:</b></td><td class="aws" valign="middle"><span style="font-size: 14px;">Month Jan 2025</span></td></tr>
</table>
</td></tr></table>
</form><br />

<table>
<tr><td class="aws"><a href="javascript:parent.window.close();">Close window</a></td></tr>
</table>

<a name="errors404">&nbsp;</a><br />
<table class="aws_border sortable" border="0" cellpadding="2" cellspacing="0" width="100%">
<tr><td class="aws_title" width="70%">Required but not found URLs (HTTP code 404) </td><td class="aws_blank">&nbsp;</td></tr>
<tr><td colspan="2">
<table class="aws_data" border="1" cellpadding="2" cellspacing="0" width="100%">
<tr bgcolor="#ECECEC"><th>URL (151)</th><th bgcolor="#66DDEE">Error&nbsp;Hits</th><th bgcolor="#4477DD" width="80">Referrers</th></tr>
<tr><td class="aws">/</td><td>371</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env</td><td>86</td><td class="aws">-</td></tr>
<tr><td class="aws">/.git/config</td><td>55</td><td class="aws">-</td></tr>
<tr><td class="aws">/ecp/Current/exporttool/microsoft.exchange.ediscovery.exporttool.application</td><td>21</td><td class="aws">-</td></tr>
<tr><td class="aws">/dns-query</td><td>20</td><td class="aws">-</td></tr>
<tr><td class="aws">/version</td><td>19</td><td class="aws">-</td></tr>
<tr><td class="aws">/telescope/requests</td><td>17</td><td class="aws">-</td></tr>
<tr><td class="aws">/info.php</td><td>17</td><td class="aws">-</td></tr>
<tr><td class="aws">/config.json</td><td>17</td><td class="aws">-</td></tr>
<tr><td class="aws">/debug/default/view</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/login.action</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/about</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/_mail/.DS_Store</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/server</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/v2/_catalog</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/.vscode/sftp.json</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/actuator/env</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/s/0343e28363e2236313e2239313/_/</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/_all_dbs</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/client/.DS_Store</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/vendor/.DS_Store</td><td>16</td><td class="aws">-</td></tr>
<tr><td class="aws">/resolve</td><td>12</td><td class="aws">-</td></tr>
<tr><td class="aws">/query</td><td>12</td><td class="aws">-</td></tr>
<tr><td class="aws">/cgi-bin/luci/</td><td>8</td><td class="aws">-</td></tr>
<tr><td class="aws">/_profiler/phpinfo</td><td>8</td><td class="aws">-</td></tr>
<tr><td class="aws">/sdk</td><td>5</td><td class="aws">-</td></tr>
<tr><td class="aws">/actuator/gateway/routes</td><td>5</td><td class="aws">-</td></tr>
<tr><td class="aws">/geoserver/web/</td><td>5</td><td class="aws">-</td></tr>
<tr><td class="aws">/ads.txt</td><td>4</td><td class="aws">-</td></tr>
<tr><td class="aws">/evox/about</td><td>3</td><td class="aws">-</td></tr>
<tr><td class="aws">/HNAP1</td><td>3</td><td class="aws">-</td></tr>
<tr><td class="aws">/autodiscover/autodiscover.json</td><td>2</td><td class="aws">-</td></tr>
<tr><td class="aws">/servlet/~ic/bsh.servlet.BshServlet</td><td>2</td><td class="aws">-</td></tr>
<tr><td class="aws">/login</td><td>2</td><td class="aws">-</td></tr>
<tr><td class="aws">/actuator/health</td><td>2</td><td class="aws">-</td></tr>
<tr><td class="aws">/sys/ui/extend/varkind/custom.jsp</td><td>2</td><td class="aws">-</td></tr>
<tr><td class="aws">/.well-known/security.txt</td><td>2</td><td class="aws">-</td></tr>
<tr><td class="aws">/owa/auth/x.js</td><td>2</td><td class="aws">-</td></tr>
<tr><td class="aws">/sitemap.xml</td><td>2</td><td class="aws">-</td></tr>
<tr><td class="aws">/css/images/PTZOptics_powerby.png</td><td>2</td><td class="aws">-</td></tr>
<tr><td class="aws">/RDWeb/Pages/</td><td>2</td><td class="aws">-</td></tr>
<tr><td class="aws">/index.html</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/dana-cached/hc/HostCheckerInstaller.osx</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/mifs/user/login.jsp</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.ENV.example</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.dev.local</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/dashboard.html</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.Config.yaml</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.live</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Core/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/api/sonicos/tfa</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env_1</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.prod</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env_sample</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/auth1.html</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/database.zip</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/oBECXJwP.jsp</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">//libs/js/iframe.js</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/CORE/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/admin.zip</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/jquery-3.3.1.slim.min.js</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/dump.sql</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Credentials</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.save</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/api/v1/pods</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.dev</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Rapid7/JBoss/version-check-llHWNn.html</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/aab8</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/APPLICATION/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/wsman</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/CRM/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/app-ads.txt</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/general/index/UploadFile.php</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/owa/auth/logon.aspx</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.backup</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.stage</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.Env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.git/HEAD</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Config/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/api/sonicos/auth</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.production</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.Aws/credentials</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/manage/account/login</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/sslvpnLogin.html</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/ADMIN/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/images/logo/logo-eoffice.php</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.old</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/RFPu</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Admin/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.prod.local</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Remote</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/auth.html</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/api/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.EXAMPLE</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/odinhttpcall1736169226</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/seeyon/htmlofficeservlet</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/dana-na/auth/url_default/welcome.cgi</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/nmaplowercheck1736217168</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.local</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/RDWeb/Pages/en-US/login.aspx</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/administrator.zip</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.ENV.EXAMPLE</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Kiw1</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/CONFIG/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.192.162</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/mics/login.jsp</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/cgi-bin/rpc</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/odinhttpcall1736148187</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/sellers.json</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/dana-na/nc/nc_gina_ver.txt</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/backup.zip</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.ENV</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/themes/default/assets/favicon/apple-touch-icon.png</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/WebInterface/login.html</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Application/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.development.local</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/seeyon/test123456.jsp</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.www</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/CREDENTIALS</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/public/vendor/laravel-filemanager/js/script.js</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/DATA/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/vsapres/web20/core/login.aspx</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.AWS/credentials</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Public/home/<USER>/check.js</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/en/welcomeRes.js</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/console/login/LoginForm.jsp</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.BAK</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/ReportServer</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/vpnsvc/connect.cgi</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/dana-na/auth/url_admin/welcome.cgi</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/static/admin/javascript/hetong.js</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.CONFIG.yaml</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/remote/login</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/owa/auth/errorFE.aspx</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.example</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/RDWeb</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.production.local</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Cp/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/jquery-3.3.2.slim.min.js</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/BACKEND/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/dyndata.js</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/servlet</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Crm/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/aab9</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/index.php</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/vendor/laravel-filemanager/js/script.js</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.env.68</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/.AWS/CREDENTIALS</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">//vpn/index.html</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/Data/.env</td><td>1</td><td class="aws">-</td></tr>
<tr><td class="aws">/CP/.env</td><td>1</td><td class="aws">-</td></tr>
</table></td></tr></table><br />

<br /><br />
<span dir="ltr" style="font: 11px verdana, arial, helvetica; color: #000000;"><b>Advanced Web Statistics 7.8 (build 20200416)</b> - <a href="http://www.awstats.org" target="awstatshome">Created by awstats (plugins: hashfiles)</a></span><br />

<br />
</body>
</html>
