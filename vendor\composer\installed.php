<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.12.1',
            'version' => '0.12.1.0',
            'reference' => 'f510c0a40911935b77b86859eb5223d58d660df1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.10.0',
            'version' => '6.10.0.0',
            'reference' => 'a49db6f0a5033aef5143295342f1c95521b075ff',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v2.6.3',
            'version' => '2.6.3.0',
            'reference' => '58c3f47f650c94ec05a151692652a868995d2938',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/sodium_compat' => array(
            'pretty_version' => 'v1.20.0',
            'version' => '1.20.0.0',
            'reference' => 'e592a3e06d1fa0d43988c7c7d9948ca836f644b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/sodium_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'e616d01114759c4c489f93b099585439f795fe35',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'fe5ea303b0887d5caefd3d431c3e61ad47037001',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'spomky-labs/aes-key-wrap' => array(
            'pretty_version' => 'v7.0.0',
            'version' => '7.0.0.0',
            'reference' => 'fbeb834b1f83aa8fbdfbd4c12124f71d4c1606ae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spomky-labs/aes-key-wrap',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spomky-labs/pki-framework' => array(
            'pretty_version' => '1.1.1',
            'version' => '1.1.1.0',
            'reference' => '86102bdd19379b2c6e5b0feb94fd490d40e7d133',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spomky-labs/pki-framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/config' => array(
            'pretty_version' => 'v7.0.4',
            'version' => '7.0.4.0',
            'reference' => '44deeba7233f08f383185ffa37dace3b3bc87364',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.0.4',
            'version' => '7.0.4.0',
            'reference' => '6b099f3306f7c9c2d2786ed736d0026b2903205f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dependency-injection' => array(
            'pretty_version' => 'v7.0.4',
            'version' => '7.0.4.0',
            'reference' => '47f37af245df8457ea63409fc242b3cc825ce5eb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dependency-injection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.4.0',
            'version' => '3.4.0.0',
            'reference' => '7c3aff79d10325257a001fcf92d991f24fc967cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v7.0.4',
            'version' => '7.0.4.0',
            'reference' => '677b24759decff69e65b1e9d1471d90f95ced880',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v7.0.3',
            'version' => '7.0.3.0',
            'reference' => '834c28d533dd0636f910909d01b9ff45cc094b5e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.4.0',
            'version' => '3.4.0.0',
            'reference' => 'a76aed96a42d2b521153fb382d418e30d18b59df',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v7.0.3',
            'version' => '7.0.3.0',
            'reference' => '2890e3a825bc0c0558526c04499c13f83e1b6b12',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client' => array(
            'pretty_version' => 'v7.0.5',
            'version' => '7.0.5.0',
            'reference' => '425f462a59d8030703ee04a9e1c666575ed5db3b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client-contracts' => array(
            'pretty_version' => 'v3.4.0',
            'version' => '3.4.0.0',
            'reference' => '1ee70e699b41909c209a0c930f11034b93578654',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0',
            ),
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v7.0.4',
            'version' => '7.0.4.0',
            'reference' => '439fdfdd344943254b1ef6278613e79040548045',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v7.0.5',
            'version' => '7.0.5.0',
            'reference' => '37c24ca28f65e3121a68f3dd4daeb36fb1fa2a72',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'ef4d7e442ca910c4764bce785146269b30cb5fc4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '32a9da87d7b3245e09ac426c83d334ae9f06f80f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'bc45c394692b948b4d383a08d7753968bed9a83d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '9773676c8a1bb1f8d4340a62efe641cf76eda7ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '87b68208d5c1188808dd7839ee1e6c8ec3b02f1b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '86fcae159633351e5fd145d1c47de6c528f8caff',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.4.1',
            'version' => '3.4.1.0',
            'reference' => 'fe07cbc8d837f60caf7018068e350cc5163681a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0|3.0',
            ),
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.0.4',
            'version' => '7.0.4.0',
            'reference' => 'f5832521b998b0bec40bee688ad5de98d4cf111b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v7.0.4',
            'version' => '7.0.4.0',
            'reference' => 'e03ad7c1535e623edbb94c22cc42353e488c6670',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v7.0.4',
            'version' => '7.0.4.0',
            'reference' => 'dfb0acb6803eb714f05d97dd4c5abe6d5fa9fe41',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'web-token/encryption-pack' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-bundle' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-checker' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-core' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-encryption-algorithm-aescbc' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-encryption-algorithm-aesgcm' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-encryption-algorithm-aesgcmkw' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-encryption-algorithm-aeskw' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-encryption-algorithm-dir' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-encryption-algorithm-ecdh-es' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-encryption-algorithm-experimental' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-encryption-algorithm-pbes2' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-encryption-algorithm-rsa' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-experimental' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-framework' => array(
            'pretty_version' => '3.3.1',
            'version' => '3.3.1.0',
            'reference' => '1dbef13afb91a576d5ce431e5f9570183b19c0dd',
            'type' => 'symfony-bundle',
            'install_path' => __DIR__ . '/../web-token/jwt-framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'web-token/jwt-key-mgmt' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-library' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-nested-token' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-signature' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-signature-algorithm-ecdsa' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-signature-algorithm-eddsa' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-signature-algorithm-experimental' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-signature-algorithm-hmac' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-signature-algorithm-none' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-signature-algorithm-rsa' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-signature-pack' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/jwt-util-ecc' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
        'web-token/signature-pack' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '3.3.1',
            ),
        ),
    ),
);
