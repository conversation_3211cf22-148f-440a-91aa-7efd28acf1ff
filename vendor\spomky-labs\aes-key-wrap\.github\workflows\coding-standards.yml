name: Coding Standards

on: [push]

jobs:
  tests:
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: [ubuntu-latest]
        php-versions: ['8.0']
    name: PHP ${{ matrix.php-versions }} Test on ${{ matrix.operating-system }}

    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          ref: ${{ github.head_ref }}

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: json, mbstring, openssl, sqlite3
          coverage: xdebug

      - name: Install Composer dependencies
        run: |
          composer update --no-progress --no-suggest --prefer-dist --optimize-autoloader

      - name: CONDING STANDARDS
        run: make ci-cs
