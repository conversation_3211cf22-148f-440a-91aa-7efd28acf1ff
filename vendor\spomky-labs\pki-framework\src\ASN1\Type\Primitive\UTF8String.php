<?php

declare(strict_types=1);

namespace Spomky<PERSON>abs\Pki\ASN1\Type\Primitive;

use Spomky<PERSON>abs\Pki\ASN1\Type\PrimitiveString;
use SpomkyLabs\Pki\ASN1\Type\UniversalClass;

/**
 * Implements *UTF8String* type.
 *
 * UTF8String* is an Unicode string with UTF-8 encoding.
 */
final class UTF8String extends PrimitiveString
{
    use UniversalClass;

    private function __construct(string $string)
    {
        parent::__construct(self::TYPE_UTF8_STRING, $string);
    }

    public static function create(string $string): self
    {
        return new self($string);
    }

    protected function validateString(string $string): bool
    {
        return mb_check_encoding($string, 'UTF-8');
    }
}
