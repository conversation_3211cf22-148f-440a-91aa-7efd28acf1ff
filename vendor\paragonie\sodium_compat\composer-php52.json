{"name": "paragonie/sodium_compat", "description": "Pure PHP implementation of libsodium; uses the PHP extension if it exists", "keywords": ["PHP", "cryptography", "elliptic curve", "elliptic curve cryptography", "Pure-PHP cryptography", "side-channel resistant", "Curve25519", "X25519", "ECDH", "Elliptic Curve <PERSON>-<PERSON><PERSON>", "Ed25519", "RFC 7748", "RFC 8032", "EdDSA", "Edwards-curve Digital Signature Algorithm", "ChaCha20", "Salsa20", "Xchacha20", "Xsalsa20", "Poly1305", "BLAKE2b", "public-key cryptography", "secret-key cryptography", "AEAD", "Chapoly", "Salpoly", "ChaCha20-Poly1305", "XSalsa20-Poly1305", "XChaCha20-Poly1305", "encryption", "authentication", "libsodium"], "license": "ISC", "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "autoload": {"files": ["autoload.php"]}, "repositories": [{"type": "git", "url": "https://github.com/garex/phpunit"}, {"type": "git", "url": "https://github.com/garex/phpunit-mock-objects"}], "require": {"php": "^5.2.4|^5.3|^5.4|^5.5|^5.6|^7|^8", "xrstf/composer-php52": "1.*", "paragonie/random_compat": ">=1"}, "minimum-stability": "dev", "require-dev": {"phpunit/phpunit-php52": "dev-3.6.12-php52", "phpunit/phpunit-mock-objects-php52": "dev-1.1.0-php52"}, "scripts": {"post-install-cmd": ["xrstf\\Composer52\\Generator::onPostInstallCmd"], "post-update-cmd": ["xrstf\\Composer52\\Generator::onPostInstallCmd"], "post-autoload-dump": ["xrstf\\Composer52\\Generator::onPostInstallCmd"]}, "suggest": {"ext-libsodium": "PHP < 7.0: Better performance, password hashing (Argon2i), secure memory management (memzero), and better security.", "ext-sodium": "PHP >= 7.0: Better performance, password hashing (Argon2i), secure memory management (memzero), and better security."}}